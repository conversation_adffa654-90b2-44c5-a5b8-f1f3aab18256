import { useState } from 'react'
import styled, { keyframes } from 'styled-components'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'

// Styled components
const AppContainer = styled.div`
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
`

const LogoContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
`

const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`

const Logo = styled.img`
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;

  &:hover {
    filter: drop-shadow(0 0 2em #646cffaa);
  }

  &.react {
    animation: ${spin} infinite 20s linear;

    &:hover {
      filter: drop-shadow(0 0 2em #61dafbaa);
    }
  }
`

const Title = styled.h1`
  font-size: 3.2em;
  line-height: 1.1;
  margin-bottom: 2rem;
`

const Card = styled.div`
  padding: 2em;
  background: #242424;
  border-radius: 8px;
  margin-bottom: 2rem;
`

const CountButton = styled.button`
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: white;
  cursor: pointer;
  transition: border-color 0.25s;

  &:hover {
    border-color: #646cff;
  }

  &:focus,
  &:focus-visible {
    outline: 4px auto -webkit-focus-ring-color;
  }
`

const Description = styled.p`
  margin-top: 1rem;
  color: #888;
`

const ReadTheDocs = styled.p`
  color: #888;
  font-size: 0.9em;
`

function App() {
  const [count, setCount] = useState(0)

  return (
    <AppContainer>
      <LogoContainer>
        <a href="https://vite.dev" target="_blank">
          <Logo src={viteLogo} alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <Logo src={reactLogo} className="react" alt="React logo" />
        </a>
      </LogoContainer>
      <Title>Vite + React + Styled Components</Title>
      <Card>
        <CountButton onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </CountButton>
        <Description>
          Edit <code>src/App.tsx</code> and save to test HMR
        </Description>
      </Card>
      <ReadTheDocs>
        Click on the Vite and React logos to learn more
      </ReadTheDocs>
    </AppContainer>
  )
}

export default App
